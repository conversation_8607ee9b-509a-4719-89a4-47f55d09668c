# src/core/models/__init__.py
"""Models Module for Ultimate Electrical Designer.

This module imports all database models to ensure they are registered
with SQLAlchemy's metadata for Alembic migrations and other operations.
"""

# Import base classes
from .base import Base, CommonColumns, EnumType, SoftDeleteColumns
from .general.component import Component
from .general.project import Project

# Import all model classes to register them with SQLAlchemy metadata
from .general.user import User, UserPreference

# Export commonly used classes
__all__ = [
    "Base",
    "CommonColumns",
    "SoftDeleteColumns",
    "EnumType",
    "User",
    "UserPreference",
    "Project",
    "Component",
]
