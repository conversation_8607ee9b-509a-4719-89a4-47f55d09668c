# src/core/repositories/general/component_repository.py
"""Component Repository.

This module provides data access layer for Component entities, extending the base
repository with component-specific query methods and operations for electrical
component management.
"""

from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func, or_, select, update
from sqlalchemy.orm import Session

from src.config.logging_config import logger
from src.core.enums.electrical_enums import ComponentCategoryType, ComponentType
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.component import Component
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository
from src.core.utils.pagination_utils import PaginationParams, PaginationResult


class ComponentRepository(BaseRepository[Component]):
    """Repository for Component entity data access operations.

    Extends BaseRepository with component-specific query methods and
    enhanced error handling for electrical component operations.
    """

    def __init__(self, db_session: Session):
        """Initialize the Component repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, Component)
        logger.debug("ComponentRepository initialized")

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_by_type(
        self, component_type: ComponentType, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get components by type.

        Args:
            component_type: Component type enum
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components of the specified type

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components by type: {component_type}")

        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.component_type == component_type,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} components of type: {component_type}")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_by_category(
        self, category: ComponentCategoryType, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get components by category.

        Args:
            category: Component category enum
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components in the specified category

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components by category: {category}")

        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.category == category,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} components in category: {category}")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_by_manufacturer(
        self, manufacturer: str, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get components by manufacturer.

        Args:
            manufacturer: Manufacturer name
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components from the specified manufacturer

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components by manufacturer: {manufacturer}")

        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.manufacturer.ilike(f"%{manufacturer}%"),
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} components from manufacturer: {manufacturer}")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_by_part_number(self, part_number: str) -> Optional[Component]:
        """Get component by part number.

        Args:
            part_number: Component part number

        Returns:
            Optional[Component]: Component with the specified part number or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving component by part number: {part_number}")

        stmt = select(self.model).where(
            and_(
                self.model.part_number == part_number,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = self.db_session.scalar(stmt)

        if result:
            logger.debug(f"Component found for part number: {part_number}")
        else:
            logger.debug(f"No component found for part number: {part_number}")

        return result

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_preferred_components(
        self, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get preferred components.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of preferred components

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving preferred components: skip={skip}, limit={limit}")

        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.is_preferred == True,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} preferred components")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def search_components(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Search components by name, description, manufacturer, or part number.

        Args:
            search_term: Search term to match against component fields
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components matching the search term

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Searching components with term: {search_term}, skip={skip}, limit={limit}"
        )

        # Use ilike for case-insensitive search
        search_pattern = f"%{search_term}%"
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                    or_(
                        self.model.name.ilike(search_pattern),
                        self.model.description.ilike(search_pattern),
                        self.model.manufacturer.ilike(search_pattern),
                        self.model.part_number.ilike(search_pattern),
                    ),
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Found {len(results)} components matching search term: {search_term}")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_components_by_specifications(
        self,
        specifications: Dict[str, Any],
        skip: int = 0,
        limit: int = 100
    ) -> List[Component]:
        """Get components matching specific technical specifications.

        Args:
            specifications: Dictionary of specification criteria
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components matching the specifications

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components by specifications: {specifications}")

        # Start with base query for active components
        stmt = select(self.model).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )

        # Add JSON specification filters with enhanced support
        for spec_path, spec_value in specifications.items():
            if spec_value is not None:
                # Support nested path queries (e.g., "electrical.voltage_rating")
                path_parts = spec_path.split('.')
                
                if len(path_parts) == 1:
                    # Simple key lookup in specifications root
                    stmt = stmt.where(
                        self.model.specifications.op('->>')(spec_path) == str(spec_value)
                    )
                elif len(path_parts) == 2:
                    # Nested lookup (e.g., electrical.voltage_rating)
                    category, key = path_parts
                    stmt = stmt.where(
                        self.model.specifications.op('->')('electrical').op('->>')(key) == str(spec_value)
                    )
                elif len(path_parts) == 3:
                    # Deep nested lookup (e.g., electrical.voltage_rating.value)
                    category, subcategory, key = path_parts
                    stmt = stmt.where(
                        self.model.specifications.op('->')('electrical').op('->')(subcategory).op('->>')(key) == str(spec_value)
                    )

        stmt = stmt.offset(skip).limit(limit).order_by(self.model.name)
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} components matching specifications")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def search_components_advanced(
        self,
        search_filters: Dict[str, Any],
        specification_filters: Optional[Dict[str, Any]] = None,
        price_range: Optional[Dict[str, float]] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Component]:
        """Advanced component search with multiple filter types.

        Args:
            search_filters: Basic search filters (category, type, manufacturer, etc.)
            specification_filters: Technical specification filters
            price_range: Price range filters with min/max values
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components matching all filters

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Advanced component search with filters: {search_filters}")

        # Start with base query for active components
        conditions = [
            self.model.is_active == True,
            self.model.is_deleted == False,
        ]

        # Apply basic search filters
        if search_filters.get("category"):
            conditions.append(self.model.category == search_filters["category"])
        
        if search_filters.get("component_type"):
            conditions.append(self.model.component_type == search_filters["component_type"])
        
        if search_filters.get("manufacturer"):
            conditions.append(self.model.manufacturer.ilike(f"%{search_filters['manufacturer']}%"))
        
        if search_filters.get("is_preferred") is not None:
            conditions.append(self.model.is_preferred == search_filters["is_preferred"])
        
        if search_filters.get("stock_status"):
            conditions.append(self.model.stock_status == search_filters["stock_status"])
        
        if search_filters.get("search_term"):
            search_pattern = f"%{search_filters['search_term']}%"
            conditions.append(
                or_(
                    self.model.name.ilike(search_pattern),
                    self.model.description.ilike(search_pattern),
                    self.model.manufacturer.ilike(search_pattern),
                    self.model.part_number.ilike(search_pattern),
                )
            )

        # Apply price range filters
        if price_range:
            currency = price_range.get("currency", "USD")
            conditions.append(self.model.currency == currency)
            
            if price_range.get("min_price") is not None:
                conditions.append(self.model.unit_price >= price_range["min_price"])
            
            if price_range.get("max_price") is not None:
                conditions.append(self.model.unit_price <= price_range["max_price"])

        # Build base query
        stmt = select(self.model).where(and_(*conditions))

        # Apply specification filters
        if specification_filters:
            for spec_path, spec_value in specification_filters.items():
                if spec_value is not None:
                    path_parts = spec_path.split('.')
                    
                    if len(path_parts) == 1:
                        stmt = stmt.where(
                            self.model.specifications.op('->>')(spec_path) == str(spec_value)
                        )
                    elif len(path_parts) == 2:
                        category, key = path_parts
                        stmt = stmt.where(
                            self.model.specifications.op('->')('electrical').op('->>')(key) == str(spec_value)
                        )

        # Apply pagination and ordering
        stmt = stmt.offset(skip).limit(limit).order_by(self.model.name)
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Advanced search returned {len(results)} components")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_manufacturers_list(self) -> List[str]:
        """Get list of unique manufacturers from active components.

        Returns:
            List[str]: List of unique manufacturer names

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Retrieving unique manufacturers list")

        stmt = (
            select(self.model.manufacturer)
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .distinct()
            .order_by(self.model.manufacturer)
        )
        
        results = list(self.db_session.scalars(stmt).all())
        logger.debug(f"Retrieved {len(results)} unique manufacturers")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def bulk_create_components(self, components_data: List[Dict[str, Any]]) -> List[Component]:
        """Create multiple components in a single transaction.

        Args:
            components_data: List of component data dictionaries

        Returns:
            List[Component]: List of created components

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Bulk creating {len(components_data)} components")

        created_components = []
        
        try:
            for component_data in components_data:
                component = self.model(**component_data)
                self.db_session.add(component)
                created_components.append(component)
            
            # Flush to get IDs but don't commit yet
            self.db_session.flush()
            
            logger.debug(f"Bulk created {len(created_components)} components")
            return created_components
            
        except Exception as e:
            logger.error(f"Error in bulk component creation: {e}")
            self.db_session.rollback()
            raise

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def bulk_update_components(
        self,
        component_ids: List[int],
        update_data: Dict[str, Any]
    ) -> int:
        """Update multiple components with the same data.

        Args:
            component_ids: List of component IDs to update
            update_data: Data to update for all components

        Returns:
            int: Number of components updated

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Bulk updating {len(component_ids)} components")

        stmt = (
            update(self.model)
            .where(self.model.id.in_(component_ids))
            .values(**update_data)
        )

        result = self.db_session.execute(stmt)
        updated_count = result.rowcount

        logger.debug(f"Bulk updated {updated_count} components")
        return updated_count

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_components_in_price_range(
        self, 
        min_price: Optional[float] = None, 
        max_price: Optional[float] = None,
        currency: str = "EUR",
        skip: int = 0, 
        limit: int = 100
    ) -> List[Component]:
        """Get components within a price range.

        Args:
            min_price: Minimum price (optional)
            max_price: Maximum price (optional)
            currency: Currency code (default: EUR)
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components within the price range

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components in price range: {min_price}-{max_price} {currency}")

        conditions = [
            self.model.is_active == True,
            self.model.is_deleted == False,
            self.model.currency == currency,
        ]

        if min_price is not None:
            conditions.append(self.model.unit_price >= min_price)
        if max_price is not None:
            conditions.append(self.model.unit_price <= max_price)

        stmt = (
            select(self.model)
            .where(and_(*conditions))
            .offset(skip)
            .limit(limit)
            .order_by(self.model.unit_price)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} components in price range")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def count_components_by_category(self) -> Dict[ComponentCategoryType, int]:
        """Count components by category.

        Returns:
            Dict[ComponentCategoryType, int]: Dictionary mapping categories to counts

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Counting components by category")

        stmt = (
            select(self.model.category, func.count(self.model.id))
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .group_by(self.model.category)
        )
        
        results = self.db_session.execute(stmt).all()
        category_counts = {category: count for category, count in results}

        logger.debug(f"Component counts by category: {category_counts}")
        return category_counts

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def count_active_components(self) -> int:
        """Count total number of active components.

        Returns:
            int: Total count of active components

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Counting active components")

        stmt = select(func.count(self.model.id)).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = self.db_session.scalar(stmt)
        count = result if result is not None else 0

        logger.debug(f"Total active components count: {count}")
        return count

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def update_component_status(self, component_id: int, is_active: bool) -> bool:
        """Update component active status.

        Args:
            component_id: Component ID
            is_active: New active status

        Returns:
            bool: True if status was updated, False if component not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Updating status for component {component_id} to {is_active}")

        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_active=is_active)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Status updated for component {component_id}")
            return True
        logger.debug(f"Component {component_id} not found for status update")
        return False

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def update_preferred_status(self, component_id: int, is_preferred: bool) -> bool:
        """Update component preferred status.

        Args:
            component_id: Component ID
            is_preferred: New preferred status

        Returns:
            bool: True if status was updated, False if component not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Updating preferred status for component {component_id} to {is_preferred}")

        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_preferred=is_preferred)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Preferred status updated for component {component_id}")
            return True
        logger.debug(f"Component {component_id} not found for preferred status update")
        return False

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def soft_delete_component(self, component_id: int) -> bool:
        """Soft delete a component.

        Args:
            component_id: Component ID

        Returns:
            bool: True if component was soft deleted, False if component not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Soft deleting component {component_id}")

        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_deleted=True, is_active=False)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Component {component_id} soft deleted successfully")
            return True
        logger.debug(f"Component {component_id} not found for soft deletion")
        return False

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def restore_component(self, component_id: int) -> bool:
        """Restore a soft deleted component.

        Args:
            component_id: Component ID

        Returns:
            bool: True if component was restored, False if component not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Restoring component {component_id}")

        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_deleted=False, is_active=True)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Component {component_id} restored successfully")
            return True
        logger.debug(f"Component {component_id} not found for restoration")
        return False

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_components_paginated_with_filters(
        self,
        pagination_params: PaginationParams,
        category: Optional[ComponentCategoryType] = None,
        component_type: Optional[ComponentType] = None,
        manufacturer: Optional[str] = None,
        is_preferred: Optional[bool] = None,
        search_term: Optional[str] = None,
    ) -> PaginationResult:
        """Get paginated components with advanced filtering.

        Args:
            pagination_params: Pagination parameters
            category: Filter by category (optional)
            component_type: Filter by component type (optional)
            manufacturer: Filter by manufacturer (optional)
            is_preferred: Filter by preferred status (optional)
            search_term: Search term for name/description/part number (optional)

        Returns:
            PaginationResult: Paginated results with metadata

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Getting paginated components with filters")

        # Build filter conditions
        filters: Dict[str, Any] = {
            "is_active": True,
            "is_deleted": False,
        }

        if category is not None:
            filters["category"] = category
        if component_type is not None:
            filters["component_type"] = component_type
        if manufacturer is not None:
            filters["manufacturer"] = manufacturer
        if is_preferred is not None:
            filters["is_preferred"] = is_preferred

        # Use base repository's paginated method with search if needed
        if search_term:
            searchable_fields = ["name", "description", "manufacturer", "part_number"]
            return self.search_paginated(
                search_term=search_term,
                searchable_fields=searchable_fields,
                pagination_params=pagination_params,
                additional_filters=filters,
            )
        else:
            return self.get_paginated(
                pagination_params=pagination_params,
                filters=filters,
            )