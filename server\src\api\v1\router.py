# src/api/v1/router.py
"""API Version 1 Router Configuration.

This module configures the v1 API router for the Ultimate Electrical Designer
backend application. It aggregates all v1 API endpoints and provides a
centralized routing configuration with proper versioning.
"""

from fastapi import APIRouter, status

from src.config.logging_config import logger
from src.core.monitoring.unified_performance_monitor import monitor_utility_performance

# Import all v1 route modules
from .auth_routes import router as auth_router
from .component_routes import router as component_router
from .health_routes import router as health_router
from .user_routes import router as user_router


@monitor_utility_performance("v1_api_router_initialization")
def create_v1_router() -> APIRouter:
    """Create and configure the v1 API router with all sub-routers.

    This function creates the main v1 API router and includes all
    endpoint routers with proper prefixes and tags.

    Returns:
        APIRouter: Configured v1 API router with all endpoints
    """
    logger.info("Initializing v1 API router with unified patterns")

    # Create the main v1 router
    v1_router = APIRouter(
        prefix="/v1",
        responses={
            status.HTTP_500_INTERNAL_SERVER_ERROR: {
                "description": "Internal server error"
            }
        },
    )

    # Include health check routes (no authentication required)
    v1_router.include_router(health_router, tags=["Health Check"])

    # Include authentication routes (no authentication required for login)
    v1_router.include_router(auth_router, tags=["Authentication"])

    # Include user management routes (authentication required)
    v1_router.include_router(user_router, tags=["User Management"])

    # Include admin CRUD routes (admin authentication required)
    from src.api.v1.user_routes import admin_crud_router
    v1_router.include_router(admin_crud_router, tags=["User Management - Admin"])

    # Include component management routes (authentication required)
    v1_router.include_router(component_router, tags=["Component Management"])

    # Log successful initialization
    logger.info("v1 API router initialized successfully with all sub-routers")
    logger.debug("v1 API router includes: health, auth, users, components")

    return v1_router


# Create the v1 API router instance
v1_router = create_v1_router()


# Add a root endpoint for v1 API information
@v1_router.get(
    "/",
    summary="API v1 Information",
    description="Get information about the v1 API endpoints",
    responses={
        status.HTTP_200_OK: {
            "description": "API v1 information",
            "content": {
                "application/json": {
                    "example": {
                        "version": "1.0",
                        "title": "Ultimate Electrical Designer API v1",
                        "description": "Professional electrical design and calculation API",
                        "endpoints": {
                            "health": "/api/v1/health",
                            "auth": "/api/v1/auth",
                            "users": "/api/v1/users",
                            "components": "/api/v1/components",
                        },
                    }
                }
            },
        }
    },
)
async def get_v1_info():
    """Get information about the v1 API.

    This endpoint provides metadata about the v1 API including
    available endpoints and version information.

    Returns:
        dict: API v1 information and available endpoints
    """
    from src.config.settings import settings

    logger.debug("v1 API information requested")

    return {
        "version": "1.0",
        "title": "Ultimate Electrical Designer API v1",
        "description": "Professional electrical design and calculation API",
        "app_version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT,
        "endpoints": {
            "health": {
                "path": "/api/v1/health",
                "description": "System health monitoring endpoints",
            },
            "auth": {
                "path": "/api/v1/auth",
                "description": "Authentication and authorization endpoints",
            },
            "users": {
                "path": "/api/v1/users",
                "description": "User management endpoints",
            },
            "components": {
                "path": "/api/v1/components",
                "description": "Electrical component catalog management endpoints",
            },
        },
        "documentation": {"swagger": "/docs", "redoc": "/redoc"},
    }
