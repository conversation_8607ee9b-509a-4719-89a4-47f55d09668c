#!/usr/bin/env python3
"""Component Database Model.

This module defines the Component model for electrical component catalog management
in the Ultimate Electrical Designer application. It provides comprehensive data
storage for electrical components including specifications, pricing, and metadata.

Key Features:
- Integration with ComponentType and ComponentCategoryType enums
- Flexible JSON specifications storage for different component types
- Professional electrical design standards compliance
- Soft delete functionality with audit trails
- Comprehensive indexing for performance optimization
"""

import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import Boolean, Index, Numeric, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from src.core.enums.electrical_enums import (
    COMPONENT_TYPE_TO_CATEGORY_MAPPING,
    ComponentCategoryType,
    ComponentType,
)
from src.core.models.base import Base, CommonColumns, EnumType, SoftDeleteColumns
from src.core.utils.json_validation import FlexibleJSON


class Component(CommonColumns, SoftDeleteColumns, Base):
    """Electrical component model for professional design workflows.
    
    This model stores comprehensive information about electrical components
    including technical specifications, pricing data, and procurement details.
    It supports the full range of electrical components defined in the
    ComponentType enum and provides flexible specification storage.
    
    Attributes:
        manufacturer: Component manufacturer name
        model_number: Manufacturer's model/part number
        description: Detailed component description
        component_type: Type of component (from ComponentType enum)
        category: Component category (from ComponentCategoryType enum)
        specifications: Flexible JSON field for electrical specifications
        unit_price: Component unit price
        currency: Price currency (default: USD)
        supplier: Primary supplier information
        part_number: Supplier part number
        weight_kg: Component weight in kilograms
        dimensions_json: Physical dimensions (L x W x H)
        is_active: Whether component is active in catalog
        is_preferred: Whether component is marked as preferred
        stock_status: Current stock availability status
        version: Component data version for change tracking
        metadata: Additional metadata for component
    """

    __tablename__ = "Component"

    # Basic Information
    manufacturer: Mapped[str] = mapped_column(
        String(100), nullable=False, index=True,
        comment="Component manufacturer name"
    )
    model_number: Mapped[str] = mapped_column(
        String(100), nullable=False, index=True,
        comment="Manufacturer's model/part number"
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True,
        comment="Detailed component description"
    )

    # Classification - Using existing enums
    component_type: Mapped[ComponentType] = mapped_column(
        EnumType(ComponentType), nullable=False, index=True,
        comment="Type of electrical component"
    )
    category: Mapped[ComponentCategoryType] = mapped_column(
        EnumType(ComponentCategoryType), nullable=False, index=True,
        comment="Component category for organization"
    )

    # Specifications (Flexible JSON for different component types)
    specifications: Mapped[Optional[str]] = mapped_column(
        FlexibleJSON, nullable=True,
        comment="Electrical specifications in JSON format"
    )

    # Pricing & Procurement
    unit_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 2), nullable=True,
        comment="Component unit price"
    )
    currency: Mapped[str] = mapped_column(
        String(3), default="USD", nullable=False,
        comment="Price currency code (ISO 4217)"
    )
    supplier: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True, index=True,
        comment="Primary supplier name"
    )
    part_number: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True, index=True,
        comment="Supplier part number"
    )

    # Physical Properties
    weight_kg: Mapped[Optional[float]] = mapped_column(
        Numeric(8, 3), nullable=True,
        comment="Component weight in kilograms"
    )
    dimensions_json: Mapped[Optional[str]] = mapped_column(
        FlexibleJSON, nullable=True,
        comment="Physical dimensions (L x W x H) in JSON format"
    )

    # Status & Availability
    is_active: Mapped[bool] = mapped_column(
        Boolean, default=True, nullable=False, index=True,
        comment="Whether component is active in catalog"
    )
    is_preferred: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False, index=True,
        comment="Whether component is marked as preferred"
    )
    stock_status: Mapped[str] = mapped_column(
        String(20), default="available", nullable=False, index=True,
        comment="Current stock availability status"
    )

    # Version Control & Metadata
    version: Mapped[str] = mapped_column(
        String(20), default="1.0", nullable=False,
        comment="Component data version for change tracking"
    )
    metadata_json: Mapped[Optional[str]] = mapped_column(
        FlexibleJSON, nullable=True,
        comment="Additional metadata for component"
    )

    # Database Constraints and Indexes
    __table_args__ = (
        # Unique constraint for manufacturer + model combination (excluding soft deleted)
        UniqueConstraint(
            "manufacturer", "model_number", "is_deleted",
            name="uq_component_manufacturer_model"
        ),
        
        # Performance indexes for common queries
        Index("idx_component_type_category", "component_type", "category"),
        Index("idx_component_active_preferred", "is_active", "is_preferred"),
        Index("idx_component_manufacturer_supplier", "manufacturer", "supplier"),
        Index("idx_component_stock_active", "stock_status", "is_active"),
        Index("idx_component_search", "manufacturer", "model_number", "name"),
        
        # GIN index for JSON specifications (PostgreSQL specific, ignored by SQLite)
        Index("idx_component_specifications_gin", "specifications", postgresql_using="gin"),
        Index("idx_component_dimensions_gin", "dimensions_json", postgresql_using="gin"),
        Index("idx_component_metadata_gin", "metadata_json", postgresql_using="gin"),
    )

    def __init__(self, **kwargs):
        """Initialize Component with automatic category assignment.
        
        Args:
            **kwargs: Component attributes
            
        Raises:
            ValueError: If component_type is not valid or category mismatch
        """
        # Auto-assign category based on component type if not provided
        if "component_type" in kwargs and "category" not in kwargs:
            component_type = kwargs["component_type"]
            if isinstance(component_type, str):
                component_type = ComponentType(component_type)
            
            if component_type in COMPONENT_TYPE_TO_CATEGORY_MAPPING:
                kwargs["category"] = COMPONENT_TYPE_TO_CATEGORY_MAPPING[component_type]
            else:
                raise ValueError(f"Unknown component type: {component_type}")
        
        # Validate category matches component type if both provided
        if "component_type" in kwargs and "category" in kwargs:
            component_type = kwargs["component_type"]
            category = kwargs["category"]
            
            if isinstance(component_type, str):
                component_type = ComponentType(component_type)
            if isinstance(category, str):
                category = ComponentCategoryType(category)
            
            expected_category = COMPONENT_TYPE_TO_CATEGORY_MAPPING.get(component_type)
            if expected_category and expected_category != category:
                raise ValueError(
                    f"Category {category} does not match expected category "
                    f"{expected_category} for component type {component_type}"
                )
        
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        """String representation of Component."""
        # Handle both enum instances and string values gracefully
        component_type_str = (
            self.component_type.value
            if hasattr(self.component_type, 'value')
            else str(self.component_type)
        )
        return (
            f"<Component(id={self.id}, manufacturer='{self.manufacturer}', "
            f"model='{self.model_number}', type='{component_type_str}')>"
        )

    def __str__(self) -> str:
        """Human-readable string representation."""
        # Handle both enum instances and string values gracefully
        component_type_str = (
            self.component_type.value
            if hasattr(self.component_type, 'value')
            else str(self.component_type)
        )
        return f"{self.manufacturer} {self.model_number} ({component_type_str})"

    @property
    def full_name(self) -> str:
        """Get full component name including manufacturer and model."""
        return f"{self.manufacturer} {self.model_number}"

    @property
    def display_name(self) -> str:
        """Get display name for UI purposes."""
        if self.name and self.name != self.model_number:
            return f"{self.name} ({self.manufacturer} {self.model_number})"
        return f"{self.manufacturer} {self.model_number}"

    def is_category_valid(self) -> bool:
        """Validate that the component's category matches its type.
        
        Returns:
            bool: True if category is valid for the component type
        """
        expected_category = COMPONENT_TYPE_TO_CATEGORY_MAPPING.get(self.component_type)
        return expected_category == self.category if expected_category else True

    def get_specification_value(self, key: str, default=None):
        """Get a specific value from the specifications JSON.
        
        Args:
            key: Specification key to retrieve
            default: Default value if key not found
            
        Returns:
            Specification value or default
        """
        if not self.specifications:
            return default
        
        try:
            import json
            specs = json.loads(self.specifications) if isinstance(self.specifications, str) else self.specifications
            return specs.get(key, default)
        except (json.JSONDecodeError, AttributeError):
            return default

    def set_specification_value(self, key: str, value) -> None:
        """Set a specific value in the specifications JSON.
        
        Args:
            key: Specification key to set
            value: Value to set
        """
        import json
        
        if not self.specifications:
            specs = {}
        else:
            try:
                specs = json.loads(self.specifications) if isinstance(self.specifications, str) else self.specifications
            except (json.JSONDecodeError, AttributeError):
                specs = {}
        
        specs[key] = value
        self.specifications = json.dumps(specs)

    def get_electrical_rating(self, rating_type: str):
        """Get electrical rating from specifications.
        
        Args:
            rating_type: Type of rating (voltage, current, power, etc.)
            
        Returns:
            Rating value with unit information if available
        """
        electrical_specs = self.get_specification_value("electrical", {})
        if electrical_specs and isinstance(electrical_specs, dict):
            return electrical_specs.get(f"{rating_type}_rating")
        return None

    def is_compatible_with_standards(self, standards: list[str]) -> bool:
        """Check if component complies with specified standards.
        
        Args:
            standards: List of standard codes to check
            
        Returns:
            bool: True if component complies with all standards
        """
        component_standards = self.get_specification_value("standards_compliance", [])
        if not component_standards:
            return False
        
        return all(std in component_standards for std in standards)

    def calculate_total_cost(self, quantity: int = 1, include_tax: bool = False, tax_rate: float = 0.0) -> Optional[Decimal]:
        """Calculate total cost for specified quantity.
        
        Args:
            quantity: Number of components
            include_tax: Whether to include tax in calculation
            tax_rate: Tax rate as decimal (e.g., 0.08 for 8%)
            
        Returns:
            Total cost or None if unit_price not set
        """
        if not self.unit_price:
            return None
        
        total = self.unit_price * Decimal(quantity)
        
        if include_tax and tax_rate > 0:
            total *= (1 + Decimal(tax_rate))
        
        return total.quantize(Decimal('0.01'))  # Round to 2 decimal places