# backend/tests/test_config/test_settings.py
"""
Comprehensive tests for application settings.

This module tests the settings configuration including environment variable
handling, validation, computed properties, and database URL management.
"""

import os
import sys
import tempfile
from pathlib import Path  # Import Path
from unittest.mock import patch, Mock, PropertyMock  # Import PropertyMock

import pytest
from pydantic import ValidationError

# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
sys.path.insert(0, server_path)

from src.config.settings import Settings, get_settings

pytestmark = [pytest.mark.unit]


class TestSettingsDefaults:
    """Test suite for settings default values."""

    def test_settings_default_values_without_env(self):
        """Test that settings have correct default values when no .env file is loaded."""
        # Create settings without loading .env file
        settings = Settings(_env_file=None)

        # Application metadata
        assert settings.APP_NAME == "Heat Tracing Design Application"
        assert settings.APP_VERSION == "1.0.0"
        assert (
            settings.APP_DESCRIPTION
            == "An engineering application for industrial electrical design, calculations, and management."
        )

        # Environment and debug
        assert settings.ENVIRONMENT == "development"
        assert settings.DEBUG is True
        assert settings.APP_PORT == 8000

        # Database configuration
        assert settings.DATABASE_URL is None
        assert settings.SQLITE_DATABASE_PATH == "app_dev.db"
        assert settings.DB_ECHO is False

        # Redis and rate limiting
        assert settings.REDIS_ENABLED is False
        assert settings.REDIS_URL is None
        assert settings.RATE_LIMIT_ENABLED is True
        assert settings.RATE_LIMIT_DEFAULT_REQUESTS_PER_MINUTE == 100

        # Security
        assert settings.SECRET_KEY == "&1DeDbTClEtRDKZI8zrb5FXN8o%Bgvqr"
        assert settings.JWT_ALGORITHM == "HS256"
        assert settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES == 30

        # Logging
        assert settings.LOG_LEVEL == "INFO"

    def test_settings_with_env_file(self):
        """Test that settings load correctly from .env file."""
        # Explicitly load the .env file for this test
        settings = Settings(_env_file=os.path.join(server_path, ".env"))

        # These values should come from .env file or defaults
        assert settings.ENVIRONMENT == "development"
        assert settings.DEBUG is True
        assert settings.SQLITE_DATABASE_PATH == "app_dev.db"
        assert settings.LOG_LEVEL == "INFO"

    def test_settings_field_descriptions(self):
        """Test that settings fields have proper descriptions."""
        settings = Settings()

        # Check that field info is available
        fields = settings.model_fields

        # Test a few key fields have descriptions
        assert fields["ENVIRONMENT"].description is not None
        assert "environment" in fields["ENVIRONMENT"].description.lower()
        assert fields["DEBUG"].description is not None
        assert "debug" in fields["DEBUG"].description.lower()
        assert fields["SECRET_KEY"].description is not None
        assert "secret" in fields["SECRET_KEY"].description.lower()


class TestSettingsValidation:
    """Test suite for settings validation."""

    def test_environment_validation_valid_values(self):
        """Test environment validation with valid values."""
        valid_environments = ["development", "testing", "production"]

        for env in valid_environments:
            settings = Settings(ENVIRONMENT=env)
            assert settings.ENVIRONMENT == env

    def test_environment_validation_invalid_value(self):
        """Test environment validation with invalid value."""
        with pytest.raises(ValidationError) as exc_info:
            Settings(ENVIRONMENT="invalid_environment")

        assert "String should match pattern" in str(exc_info.value)

    def test_log_level_validation_valid_values(self):
        """Test log level validation with valid values."""
        valid_levels = ["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG", "NOTSET"]

        for level in valid_levels:
            settings = Settings(LOG_LEVEL=level)
            assert settings.LOG_LEVEL == level

    def test_log_level_validation_invalid_value(self):
        """Test log level validation with invalid value."""
        with pytest.raises(ValidationError) as exc_info:
            Settings(LOG_LEVEL="INVALID_LEVEL")

        assert "String should match pattern" in str(exc_info.value)

    def test_secret_key_minimum_length(self):
        """Test secret key minimum length validation."""
        # Valid secret key (32+ characters)
        long_key = "a" * 32
        settings = Settings(SECRET_KEY=long_key)
        assert settings.SECRET_KEY == long_key

        # Invalid secret key (too short)
        with pytest.raises(ValidationError) as exc_info:
            Settings(SECRET_KEY="short")

        assert "String should have at least 32 characters" in str(exc_info.value)

    def test_app_port_validation(self):
        """Test app port validation."""
        # Valid port
        settings = Settings(APP_PORT=8080)
        assert settings.APP_PORT == 8080

        # Test with string that can be converted to int
        settings = Settings(APP_PORT="9000")
        assert settings.APP_PORT == 9000

    def test_boolean_field_validation(self):
        """Test boolean field validation."""
        # Test DEBUG field
        settings = Settings(DEBUG=True)
        assert settings.DEBUG is True

        settings = Settings(DEBUG=False)
        assert settings.DEBUG is False

        # Test string conversion
        settings = Settings(DEBUG="true")
        assert settings.DEBUG is True

        settings = Settings(DEBUG="false")
        assert settings.DEBUG is False


class TestEffectiveDatabaseUrl:
    """Test suite for effective_database_url property."""

    def test_effective_database_url_production_with_database_url(self):
        """Test effective database URL in production with DATABASE_URL set."""
        database_url = "***************************************"
        settings = Settings(ENVIRONMENT="production", DATABASE_URL=database_url)

        assert settings.effective_database_url == database_url

    def test_effective_database_url_development_with_database_url(self):
        """Test effective database URL in development with DATABASE_URL set."""
        database_url = "**************************************"
        settings = Settings(ENVIRONMENT="development", DATABASE_URL=database_url)

        assert settings.effective_database_url == database_url

    def test_effective_database_url_testing_with_database_url(self):
        """Test effective database URL in testing with DATABASE_URL set."""
        database_url = "***************************************"
        settings = Settings(ENVIRONMENT="testing", DATABASE_URL=database_url)

        assert settings.effective_database_url == database_url

    @patch("src.config.settings.Path.mkdir")  # Mock Path.mkdir
    @patch("src.config.settings.Path.resolve")  # Mock Path.resolve
    def test_effective_database_url_no_database_url(
        self, mock_path_resolve, mock_mkdir
    ):
        """Test effective database URL when DATABASE_URL is None."""
        # Mock Path.resolve() to return a Windows-style absolute path to the settings.py file
        mock_path_resolve.return_value = Path(
            "C:/absolute/path/server/src/config/settings.py"
        )

        settings = Settings(DATABASE_URL=None)

        result = settings.effective_database_url

        # Expect 3 slashes for Windows drive letter absolute path
        assert result == "sqlite:///C:/absolute/path/server/data/app_dev.db"
        mock_mkdir.assert_called_once_with(parents=True, exist_ok=True)

    @patch("src.config.settings.Path.mkdir")  # Mock Path.mkdir
    @patch("src.config.settings.Path.resolve")  # Mock Path.resolve
    def test_get_sqlite_url_method(self, mock_path_resolve, mock_mkdir):
        """Test _get_sqlite_url method."""
        # Mock the resolved path to be the settings.py file location
        mock_path_resolve.return_value = Path(
            "/test/path/server/src/config/settings.py"
        )

        settings = Settings(SQLITE_DATABASE_PATH="database.db")

        result = settings._get_sqlite_url()

        # The method goes up 3 levels from settings.py to get server dir, then adds data/database.db
        assert result == "sqlite:////test/path/server/data/database.db"
        mock_path_resolve.assert_called_once()
        mock_mkdir.assert_called_once()

    @patch("src.config.settings.Path")
    def test_get_sqlite_url_directory_creation(self, mock_path_class):
        """Test that _get_sqlite_url creates necessary directories."""
        # Create mock instances for the path operations
        mock_current_file = Mock()
        mock_server_dir = Mock()
        mock_data_dir = Mock()
        mock_db_path = Mock()

        # Set up the path resolution chain
        mock_current_file.parent.parent.parent = mock_server_dir

        # Mock the / operator for path joining
        mock_server_dir.__truediv__ = Mock(
            return_value=mock_data_dir
        )  # server_dir / "data"
        mock_data_dir.__truediv__ = Mock(
            return_value=mock_db_path
        )  # data_dir / filename
        mock_db_path.as_posix.return_value = "/test/path/data/app_dev.db"

        # Mock the Path constructor
        mock_path_class.return_value = mock_current_file
        mock_current_file.resolve.return_value = mock_current_file

        settings = Settings()
        result = settings._get_sqlite_url()

        # Verify directory creation was called
        mock_data_dir.mkdir.assert_called_once_with(parents=True, exist_ok=True)
        assert result == "sqlite:////test/path/data/app_dev.db"


class TestEnvironmentVariableLoading:
    """Test suite for environment variable loading."""

    def test_environment_variable_override(self):
        """Test that environment variables override defaults."""
        with patch.dict(
            os.environ,
            {
                "ENVIRONMENT": "production",
                "DEBUG": "false",
                "APP_PORT": "9000",
                "LOG_LEVEL": "ERROR",
            },
        ):
            settings = Settings()

            assert settings.ENVIRONMENT == "production"
            assert settings.DEBUG is False
            assert settings.APP_PORT == 9000
            assert settings.LOG_LEVEL == "ERROR"

    def test_database_url_from_environment(self):
        """Test loading DATABASE_URL from environment."""
        database_url = "**************************************"

        with patch.dict(os.environ, {"DATABASE_URL": database_url}):
            settings = Settings()

            assert settings.DATABASE_URL == database_url
            assert settings.effective_database_url == database_url

    def test_redis_configuration_from_environment(self):
        """Test loading Redis configuration from environment."""
        with patch.dict(
            os.environ,
            {
                "REDIS_ENABLED": "true",
                "REDIS_URL": "redis://localhost:6379/0",
                "RATE_LIMIT_ENABLED": "false",
                "RATE_LIMIT_DEFAULT_REQUESTS_PER_MINUTE": "200",
            },
        ):
            settings = Settings()

            assert settings.REDIS_ENABLED is True
            assert settings.REDIS_URL == "redis://localhost:6379/0"
            assert settings.RATE_LIMIT_ENABLED is False
            assert settings.RATE_LIMIT_DEFAULT_REQUESTS_PER_MINUTE == 200

    def test_security_configuration_from_environment(self):
        """Test loading security configuration from environment."""
        secret_key = "a" * 40  # Valid secret key

        with patch.dict(
            os.environ,
            {
                "SECRET_KEY": secret_key,
                "JWT_ALGORITHM": "RS256",
                "JWT_ACCESS_TOKEN_EXPIRE_MINUTES": "60",
            },
        ):
            settings = Settings()

            assert settings.SECRET_KEY == secret_key
            assert settings.JWT_ALGORITHM == "RS256"
            assert settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES == 60


class TestSettingsConfiguration:
    """Test suite for settings configuration."""

    def test_model_config_attributes(self):
        """Test that model config is properly set."""
        settings = Settings()
        config = settings.model_config

        assert config["env_file"] == ".env"
        assert config["env_file_encoding"] == "utf-8"
        assert config["case_sensitive"] is True
        assert config["extra"] == "ignore"

    def test_case_sensitivity(self):
        """Test that environment variables are case sensitive."""
        # This test verifies the case_sensitive=True setting
        with patch.dict(
            os.environ,
            {
                "environment": "production",  # lowercase
                "ENVIRONMENT": "testing",  # uppercase
            },
        ):
            settings = Settings()

            # Should use the uppercase version due to case sensitivity
            assert settings.ENVIRONMENT == "testing"


class TestGetSettingsFunction:
    """Test suite for get_settings function."""

    def test_get_settings_returns_settings_instance(self):
        """Test that get_settings returns a Settings instance."""
        result = get_settings()

        assert isinstance(result, Settings)

    def test_get_settings_returns_same_instance(self):
        """Test that get_settings returns the same instance (singleton behavior)."""
        result1 = get_settings()
        result2 = get_settings()

        assert result1 is result2

    def test_get_settings_has_expected_attributes(self):
        """Test that get_settings result has expected attributes."""
        settings = get_settings()

        # Test a few key attributes
        assert hasattr(settings, "APP_NAME")
        assert hasattr(settings, "ENVIRONMENT")
        assert hasattr(settings, "effective_database_url")
        assert hasattr(settings, "_get_sqlite_url")


class TestSettingsIntegration:
    """Test suite for settings integration scenarios."""

    def test_development_environment_configuration(self):
        """Test typical development environment configuration."""
        settings = Settings(
            ENVIRONMENT="development", DEBUG=True, DB_ECHO=True, LOG_LEVEL="DEBUG"
        )

        assert settings.ENVIRONMENT == "development"
        assert settings.DEBUG is True
        assert settings.DB_ECHO is True
        assert settings.LOG_LEVEL == "DEBUG"

    def test_production_environment_configuration(self):
        """Test typical production environment configuration."""
        secret_key = "production_secret_key_that_is_long_enough_for_validation"
        database_url = "*****************************************"

        settings = Settings(
            ENVIRONMENT="production",
            DEBUG=False,
            SECRET_KEY=secret_key,
            DATABASE_URL=database_url,
            LOG_LEVEL="WARNING",
            RATE_LIMIT_ENABLED=True,
        )

        assert settings.ENVIRONMENT == "production"
        assert settings.DEBUG is False
        assert settings.SECRET_KEY == secret_key
        assert settings.DATABASE_URL == database_url
        assert settings.effective_database_url == database_url
        assert settings.LOG_LEVEL == "WARNING"
        assert settings.RATE_LIMIT_ENABLED is True

    def test_testing_environment_configuration(self):
        """Test typical testing environment configuration."""
        settings = Settings(
            ENVIRONMENT="testing",
            DEBUG=False,
            DB_ECHO=False,
            LOG_LEVEL="ERROR",
            RATE_LIMIT_ENABLED=False,
        )

        assert settings.ENVIRONMENT == "testing"
        assert settings.DEBUG is False
        assert settings.DB_ECHO is False
        assert settings.LOG_LEVEL == "ERROR"
        assert settings.RATE_LIMIT_ENABLED is False

    @patch("src.config.settings.Path.mkdir")  # Mock Path.mkdir
    @patch("src.config.settings.Path.resolve")  # Mock Path.resolve
    def test_sqlite_fallback_scenario(self, mock_path_resolve, mock_mkdir):
        """Test SQLite fallback scenario."""
        # Mock Path.resolve() to return a Windows-style absolute path
        mock_path_resolve.return_value = Path("C:/tmp/test_app.db")
        with patch("src.config.settings.Path.resolve", new=mock_path_resolve):
            settings = Settings(
                DATABASE_URL=None, SQLITE_DATABASE_PATH="/tmp/test_app.db"
            )

            assert settings.DATABASE_URL is None
            # Expect 3 slashes for Windows drive letter absolute path
            assert settings.effective_database_url == "sqlite:///C:/tmp/test_app.db"
            mock_mkdir.assert_called_once_with(parents=True, exist_ok=True)
